<!DOCTYPE html>
<html lang="en" ><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Notes App</title>
    <link rel="stylesheet" href="./src/style.css">
    <link rel="stylesheet" href="./src/reset.css">
    <link rel="stylesheet" href="./src/welcome.css">
    <link rel="stylesheet" href="./src/css">
    <link href="./src/css(1)" rel="stylesheet">
    <link rel="preconnect" href="https://api.fontshare.com/" crossorigin="">
    <link rel="dns-prefetch" href="https://api.fontshare.com/">
    <meta name="description" content="A minimal notes application for managing projects and files">
    <meta name="theme-color" content="#242424">

    <link rel="icon" type="image/svg+xml" href="https://noteapp-madebypeakk.web.app/assets/NoteAPPIcons.svg">
    <link rel="icon" type="image/png" href="https://noteapp-madebypeakk.web.app/assets/NoteAPPIcons.png">

<body class="dark-mode">
    <!-- Intro load -->
    

    <!-- Welcome screen (show once) -->

    <div id="welcomeOverlay" class="welcome-overlay" style="display: none;">
        <div class="welcome-container">

            <p id="welcome-animate" aria-label="Introducing NoteAPP, A simple application For effortless note-taking And swift thought organisation." style="transform: translate(-50%, -50%) scale(1); opacity: 0;"><div aria-hidden="true" style="position: relative; display: block; text-align: start; translate: none; rotate: none; scale: none; transform: translate(0px, 0%); opacity: 1;"> <div class="char" aria-hidden="true" data-content="I" style="position: relative; display: inline-block;">I</div><div class="char" aria-hidden="true" data-content="n" style="position: relative; display: inline-block;">n</div><div class="char" aria-hidden="true" data-content="t" style="position: relative; display: inline-block;">t</div><div class="char" aria-hidden="true" data-content="r" style="position: relative; display: inline-block;">r</div><div class="char" aria-hidden="true" data-content="o" style="position: relative; display: inline-block;">o</div><div class="char" aria-hidden="true" data-content="d" style="position: relative; display: inline-block;">d</div><div class="char" aria-hidden="true" data-content="u" style="position: relative; display: inline-block;">u</div><div class="char" aria-hidden="true" data-content="c" style="position: relative; display: inline-block;">c</div><div class="char" aria-hidden="true" data-content="i" style="position: relative; display: inline-block;">i</div><div class="char" aria-hidden="true" data-content="n" style="position: relative; display: inline-block;">n</div><div class="char" aria-hidden="true" data-content="g" style="position: relative; display: inline-block;">g</div> <div class="char" aria-hidden="true" data-content="N" style="position: relative; display: inline-block;">N</div><div class="char" aria-hidden="true" data-content="o" style="position: relative; display: inline-block;">o</div><div class="char" aria-hidden="true" data-content="t" style="position: relative; display: inline-block;">t</div><div class="char" aria-hidden="true" data-content="e" style="position: relative; display: inline-block;">e</div><div class="char" aria-hidden="true" data-content="A" style="position: relative; display: inline-block;">A</div><div class="char" aria-hidden="true" data-content="P" style="position: relative; display: inline-block;">P</div><div class="char" aria-hidden="true" data-content="P" style="position: relative; display: inline-block;">P</div><div class="char" aria-hidden="true" data-content="," style="position: relative; display: inline-block;">,</div></div><div aria-hidden="true" style="position: relative; display: block; text-align: start; translate: none; rotate: none; scale: none; transform: translate(0px, 0%); opacity: 1;"> <div class="char" aria-hidden="true" data-content="A" style="position: relative; display: inline-block;">A</div> <div class="char" aria-hidden="true" data-content="s" style="position: relative; display: inline-block;">s</div><div class="char" aria-hidden="true" data-content="i" style="position: relative; display: inline-block;">i</div><div class="char" aria-hidden="true" data-content="m" style="position: relative; display: inline-block;">m</div><div class="char" aria-hidden="true" data-content="p" style="position: relative; display: inline-block;">p</div><div class="char" aria-hidden="true" data-content="l" style="position: relative; display: inline-block;">l</div><div class="char" aria-hidden="true" data-content="e" style="position: relative; display: inline-block;">e</div> <strong style="display: inline-block;"><div class="char" aria-hidden="true" data-content="a" style="position: relative; display: inline-block;">a</div><div class="char" aria-hidden="true" data-content="p" style="position: relative; display: inline-block;">p</div><div class="char" aria-hidden="true" data-content="p" style="position: relative; display: inline-block;">p</div><div class="char" aria-hidden="true" data-content="l" style="position: relative; display: inline-block;">l</div><div class="char" aria-hidden="true" data-content="i" style="position: relative; display: inline-block;">i</div><div class="char" aria-hidden="true" data-content="c" style="position: relative; display: inline-block;">c</div><div class="char" aria-hidden="true" data-content="a" style="position: relative; display: inline-block;">a</div><div class="char" aria-hidden="true" data-content="t" style="position: relative; display: inline-block;">t</div><div class="char" aria-hidden="true" data-content="i" style="position: relative; display: inline-block;">i</div><div class="char" aria-hidden="true" data-content="o" style="position: relative; display: inline-block;">o</div><div class="char" aria-hidden="true" data-content="n" style="position: relative; display: inline-block;">n</div></strong></div><div aria-hidden="true" style="position: relative; display: block; text-align: start; translate: none; rotate: none; scale: none; transform: translate(0px, 0%); opacity: 1;"> <div class="char" aria-hidden="true" data-content="F" style="position: relative; display: inline-block;">F</div><div class="char" aria-hidden="true" data-content="o" style="position: relative; display: inline-block;">o</div><div class="char" aria-hidden="true" data-content="r" style="position: relative; display: inline-block;">r</div> <div class="char" aria-hidden="true" data-content="e" style="position: relative; display: inline-block;">e</div><div class="char" aria-hidden="true" data-content="f" style="position: relative; display: inline-block;">f</div><div class="char" aria-hidden="true" data-content="f" style="position: relative; display: inline-block;">f</div><div class="char" aria-hidden="true" data-content="o" style="position: relative; display: inline-block;">o</div><div class="char" aria-hidden="true" data-content="r" style="position: relative; display: inline-block;">r</div><div class="char" aria-hidden="true" data-content="t" style="position: relative; display: inline-block;">t</div><div class="char" aria-hidden="true" data-content="l" style="position: relative; display: inline-block;">l</div><div class="char" aria-hidden="true" data-content="e" style="position: relative; display: inline-block;">e</div><div class="char" aria-hidden="true" data-content="s" style="position: relative; display: inline-block;">s</div><div class="char" aria-hidden="true" data-content="s" style="position: relative; display: inline-block;">s</div> <div class="char" aria-hidden="true" data-content="n" style="position: relative; display: inline-block;">n</div><div class="char" aria-hidden="true" data-content="o" style="position: relative; display: inline-block;">o</div><div class="char" aria-hidden="true" data-content="t" style="position: relative; display: inline-block;">t</div><div class="char" aria-hidden="true" data-content="e" style="position: relative; display: inline-block;">e</div><div class="char" aria-hidden="true" data-content="-" style="position: relative; display: inline-block;">-</div><div class="char" aria-hidden="true" data-content="t" style="position: relative; display: inline-block;">t</div><div class="char" aria-hidden="true" data-content="a" style="position: relative; display: inline-block;">a</div><div class="char" aria-hidden="true" data-content="k" style="position: relative; display: inline-block;">k</div><div class="char" aria-hidden="true" data-content="i" style="position: relative; display: inline-block;">i</div><div class="char" aria-hidden="true" data-content="n" style="position: relative; display: inline-block;">n</div><div class="char" aria-hidden="true" data-content="g" style="position: relative; display: inline-block;">g</div></div><div aria-hidden="true" style="position: relative; display: block; text-align: start; translate: none; rotate: none; scale: none; transform: translate(0px, 0%); opacity: 1;"> <div class="char" aria-hidden="true" data-content="A" style="position: relative; display: inline-block;">A</div><div class="char" aria-hidden="true" data-content="n" style="position: relative; display: inline-block;">n</div><div class="char" aria-hidden="true" data-content="d" style="position: relative; display: inline-block;">d</div> <div class="char" aria-hidden="true" data-content="s" style="position: relative; display: inline-block;">s</div><div class="char" aria-hidden="true" data-content="w" style="position: relative; display: inline-block;">w</div><div class="char" aria-hidden="true" data-content="i" style="position: relative; display: inline-block;">i</div><div class="char" aria-hidden="true" data-content="f" style="position: relative; display: inline-block;">f</div><div class="char" aria-hidden="true" data-content="t" style="position: relative; display: inline-block;">t</div> <div class="char" aria-hidden="true" data-content="t" style="position: relative; display: inline-block;">t</div><div class="char" aria-hidden="true" data-content="h" style="position: relative; display: inline-block;">h</div><div class="char" aria-hidden="true" data-content="o" style="position: relative; display: inline-block;">o</div><div class="char" aria-hidden="true" data-content="u" style="position: relative; display: inline-block;">u</div><div class="char" aria-hidden="true" data-content="g" style="position: relative; display: inline-block;">g</div><div class="char" aria-hidden="true" data-content="h" style="position: relative; display: inline-block;">h</div><div class="char" aria-hidden="true" data-content="t" style="position: relative; display: inline-block;">t</div> <strong style="display: inline-block;"><div class="char" aria-hidden="true" data-content="o" style="position: relative; display: inline-block;">o</div><div class="char" aria-hidden="true" data-content="r" style="position: relative; display: inline-block;">r</div><div class="char" aria-hidden="true" data-content="g" style="position: relative; display: inline-block;">g</div><div class="char" aria-hidden="true" data-content="a" style="position: relative; display: inline-block;">a</div><div class="char" aria-hidden="true" data-content="n" style="position: relative; display: inline-block;">n</div><div class="char" aria-hidden="true" data-content="i" style="position: relative; display: inline-block;">i</div><div class="char" aria-hidden="true" data-content="s" style="position: relative; display: inline-block;">s</div><div class="char" aria-hidden="true" data-content="a" style="position: relative; display: inline-block;">a</div><div class="char" aria-hidden="true" data-content="t" style="position: relative; display: inline-block;">t</div><div class="char" aria-hidden="true" data-content="i" style="position: relative; display: inline-block;">i</div><div class="char" aria-hidden="true" data-content="o" style="position: relative; display: inline-block;">o</div><div class="char" aria-hidden="true" data-content="n" style="position: relative; display: inline-block;">n</div><div class="char" aria-hidden="true" data-content="." style="position: relative; display: inline-block;">.</div></strong> </div></p>

            <div class="hello-language" id="tagline" aria-label="HelloNoteAPP" style="opacity: 1; color: rgb(119, 140, 125);"> <strong><div aria-hidden="true" style="position: relative; display: inline-block;"><div aria-hidden="true" style="position: relative; display: inline-block; opacity: 1;">H</div><div aria-hidden="true" style="position: relative; display: inline-block; opacity: 1;">e</div><div aria-hidden="true" style="position: relative; display: inline-block; opacity: 1;">l</div><div aria-hidden="true" style="position: relative; display: inline-block; opacity: 1;">l</div><div aria-hidden="true" style="position: relative; display: inline-block; opacity: 1;">o</div></div></strong><div aria-hidden="true" style="position: relative; display: inline-block;"><div aria-hidden="true" style="position: relative; display: inline-block; opacity: 1;">N</div><div aria-hidden="true" style="position: relative; display: inline-block; opacity: 1;">o</div><div aria-hidden="true" style="position: relative; display: inline-block; opacity: 1;">t</div><div aria-hidden="true" style="position: relative; display: inline-block; opacity: 1;">e</div><div aria-hidden="true" style="position: relative; display: inline-block; opacity: 1;">A</div><div aria-hidden="true" style="position: relative; display: inline-block; opacity: 1;">P</div><div aria-hidden="true" style="position: relative; display: inline-block; opacity: 1;">P</div></div> </div>

            <button class="continue-btn" id="continue-btn" style="visibility: visible; opacity: 1;">
            Click to continue</button>
            
            <!-- closeWelcome() -->
        </div>
    </div>

    <!-- Sidebar -->
    <div class="sidebar" id="sidebar" role="navigation" aria-label="Main navigation" style="transform: translateX(0px); width: 260px;">
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <div class="logo">NotesAPP</div>
                <svg xmlns="http://www.w3.org/2000/svg" height="22px" viewBox="0 -960 960 960" width="22px" fill="currentColor" id="hideSidebarBtn" aria-label="Hide sidebar" type="button" aria-hidden="true">
                <path d="M660-320v-320L500-480l160 160ZM200-120q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h560q33 0 56.5 23.5T840-760v560q0 33-23.5 56.5T760-120H200Zm120-80v-560H200v560h120Zm80 0h360v-560H400v560Zm-80 0H200h120Z"></path>
                </svg>
            </div>

            <div class="createFilesButtons">
                <button class="btn btn-primary" type="button" id="createFileBtn" aria-describedby="create-file-desc">
                <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="currentColor"><path d="M440-440H200v-80h240v-240h80v240h240v80H520v240h-80v-240Z"></path></svg>
                New File</button>

                
                <div class="theme-toggle">
                    <div class="theme-toggle-texts">
                        <svg xmlns="http://www.w3.org/2000/svg" height="20px" viewBox="0 -960 960 960" width="20px" fill="currentColor">
                        <path d="M80 0v-160h800V0H80Zm140-280 210-560h100l210 560h-96l-50-144H368l-52 144h-96Zm176-224h168l-82-232h-4l-82 232Z"></path></svg>
                        Toggle Theme
                    </div>
                    <div class="themeToggleSwitch">
                        <input type="checkbox" id="theme-switch" class="theme-switch" aria-label="Toggle dark and light mode">
                        <label for="theme-switch" class="theme-switch-label"></label>
                    </div>
                </div>

                <button class="btn btn-primary" type="button" id="exportFileBtn" aria-describedby="create-file-desc">
                <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="currentColor"><path d="M480-320 280-520l56-58 104 104v-326h80v326l104-104 56 58-200 200ZM240-160q-33 0-56.5-23.5T160-240v-120h80v120h480v-120h80v120q0 33-23.5 56.5T720-160H240Z"></path></svg>
                Download file</button>
            </div>

            <div class="bottom-sidebar">
                <div id="localstr-moniter">
                    <span>File storage capacity</span>
                    <div id="bar-bg">
                        <div id="bar-fill" style="width: 0.0099563%; background: rgb(0, 180, 216);"></div>
                    </div>
                    <div id="moniterDisplay">522B / 5MB</div>
                    <button id="btn-clear" style="display: none;"></button>
                </div>
            </div>

        </div>

        <div class="files-section">
            <div class="files-section-label"><span>Recents</span></div>
            <div class="file-list" id="fileList"><div class="file-item active" data-file-id="welcome">
            <div class="file-info sidebarFile-infoContent">
                <div class="file-name">Welcome</div>
            </div>
            <div class="file-actions">
                <button class="action-btn" data-action="rename" data-file-id="welcome">
                    <svg xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 -960 960 960" width="18px" fill="currentColor">
                        <path d="M200-200h57l391-391-57-57-391 391v57Zm-80 80v-170l528-527q12-11 26.5-17t30.5-6q16 0 31 6t26 18l55 56q12 11 17.5 26t5.5 30q0 16-5.5 30.5T817-647L290-120H120Zm640-584-56-56 56 56Zm-141 85-28-29 57 57-29-28Z"></path>
                    </svg>
                </button>
                <button class="action-btn delete" data-action="delete" data-file-id="welcome">
                    <svg xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 -960 960 960" width="18px" fill="currentColor">
                        <path d="M280-120q-33 0-56.5-23.5T200-200v-520h-40v-80h200v-40h240v40h200v80h-40v520q0 33-23.5 56.5T680-120H280Zm400-600H280v520h400v-520ZM360-280h80v-360h-80v360Zm160 0h80v-360h-80v360ZM280-720v520-520Z"></path>
                    </svg>
                </button>
            </div>
        </div></div>
        </div>
    </div>

    <!-- Using this when toggle sidebar -->
    <div id="background-blur"></div>

    <div id="content-preview"></div>
    <div id="context-menu-overlay"></div>
    <div id="file-context-menu">
        <div class="context-menu-item" data-action="rename">Rename</div>
        <div class="context-menu-item" data-action="delete">Delete</div>
    </div>

    <div class="app-container">
        <!-- Main Content -->
        <div class="main-content" id="main-content" role="main">
            <div class="editor-header">

                <div class="editerDoing">

                    <div class="tabsFile" id="tabsFile" role="tablist" aria-label="Tabs">
                         <div class="file-list-tabs" id="fileListTabs"><div class="file-item active" data-file-id="welcome">
            <div class="file-name">Welcome</div>
            <button class="close-btn" data-action="delete" data-file-id="welcome">
                <svg xmlns="http://www.w3.org/2000/svg" height="16px" viewBox="0 -960 960 960" width="16px" fill="currentColor">
                    <path d="m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z"></path>
                </svg>
            </button>
        </div></div>
                    </div>

                    <div class="editerOperator" role="toolbar" aria-label="Editor actions">
                        <div class="file-title" id="fileTitle">Welcome</div>
                        <div class="editor-actions">
                            <button class="btn" id="saveBtn" type="button" aria-describedby="save-file-desc" aria-label="Save current file">Save</button>
                            <button class="btn" id="exportBtn">
                            Download</button>
                        </div>
                        <!-- Toogle sidebar -->
                        <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="currentColor" id="toggleSidebar">
                            <path d="M480-160q-33 0-56.5-23.5T400-240q0-33 23.5-56.5T480-320q33 0 56.5 23.5T560-240q0 33-23.5 56.5T480-160Zm0-240q-33 0-56.5-23.5T400-480q0-33 23.5-56.5T480-560q33 0 56.5 23.5T560-480q0 33-23.5 56.5T480-400Zm0-240q-33 0-56.5-23.5T400-720q0-33 23.5-56.5T480-800q33 0 56.5 23.5T560-720q0 33-23.5 56.5T480-640Z"></path>
                        </svg>
                    </div>

                </div>
            </div>

            <div class="editor-container">
                <textarea class="editor" id="editor" placeholder="Start writing your notes here..." spellcheck="false" aria-describedby="editor-help" role="textbox" aria-multiline="true"">                </textarea>
            </div>

            <div class="status-bar" role="contentinfo" aria-label="Status information">
                <div class="status-info" aria-live="polite">
                    <span id="wordCount" aria-label="Word count">Words: 1</span>
                    <span id="charCount" aria-label="Character count">Characters: 33</span>
                </div>
                <div id="fileStatus" aria-label="Current file">File: Welcome</div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <!-- Create File Modal -->
    <div class="modal" id="createFileModal" role="dialog" aria-labelledby="create-file-title" aria-describedby="create-file-description" aria-modal="true" aria-hidden="true" inert="true">
        <div class="modal-content">
            <h3>Create a new file</h3>
            <p>
                Create your note file elsewhere.<br>You can also create a file here.
            </p>
            <div class="input-group">
                <label for="fileName">File name:</label>
                    <input type="text" id="fileName" placeholder="Enter a file name" autocomplete="off" required="" aria-describedby="file-name-error">
            </div>
            <div class="modal-actions">
                <button class="btn btn-secondary btnModel" id="cancelCreateFileBtn">
                Cancel</button>
                <button class="btn btn-primary btnModel" id="confirmCreateFileBtn">
                Create</button>
            </div>
        </div>
    </div>

    <script src="./src/intro-load.js" defer=""></script>
    <script src="./src/note.js" defer=""></script>
    <script src="./src/event.js" defer=""></script>
    <script src="./src/editer.js" defer=""></script>
    <script src="./src/cursor-behavior.js"></script>
    <script src="./src/ai.js" defer=""></script>
    <script src="./src/localstr-moniter.js" defer=""></script>
    <script src="./src/event-handlers.js" defer=""></script>

    <script src="./src/gsap.min.js"></script>
    <script src="./src/SplitText.min.js"></script>
    <script src="./src/ScrambleTextPlugin.min.js"></script>
    <script src="./src/welcome.js"></script>



</body></html>