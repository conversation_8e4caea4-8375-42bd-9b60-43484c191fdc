# Math Auto-Complete Examples

## การใช้งาน Math Auto-Complete ในแอป Notes

### เครื่องหมายคณิตศาสตร์ที่รองรับ:

#### 🔢 **เครื่องหมายพื้นฐาน**
- `+` บวก
- `-` ลบ  
- `*` คูณ
- `/` หาร

#### 📱 **เครื่องหมายในโทรศัพท์**
- `×` คูณ (Alt + 0215 หรือ copy-paste)
- `÷` หาร (Alt + 0247 หรือ copy-paste)  
- `−` ลบ (Unicode minus)

### ตัวอย่างการใช้งาน:

#### การคำนวณพื้นฐาน:
```
5 × 3 =          → แสดง ghost: " 15"
12 ÷ 4 =         → แสดง ghost: " 3"
10 − 3 =         → แสดง ghost: " 7"
2 + 3 × 4 =      → แสดง ghost: " 14"
(5 + 3) × 2 =    → แสดง ghost: " 16"
```

#### ฟังก์ชันคณิตศาสตร์:
```
sqrt(25) =       → แสดง ghost: " 5"
sqrt(2×8) =      → แสดง ghost: " 4"
sin(30) =        → แสดงผลลัพธ์
cos(0) =         → แสดง ghost: " 1"
log(100) =       → แสดง ghost: " 2"
```

#### การยกกำลัง:
```
2^3 =            → แสดง ghost: " 8"
5^2 =            → แสดง ghost: " 25"
pow(2,3) =       → แสดง ghost: " 8"
```

### วิธีการใช้งาน:

1. **พิมพ์สมการ** ลงท้ายด้วย `=`
2. **รอ ghost message** ปรากฏ (สีเขียวอ่อน)
3. **คลิกที่ ghost** หรือ **กด Tab** เพื่อเติมคำตอบ
4. **กด Esc** เพื่อซ่อน ghost message

### คุณสมบัติพิเศษ:

✅ **การเว้นวรรคอัตโนมัติ**: `= 2` ไม่ใช่ `=2`  
✅ **รองรับเครื่องหมายมือถือ**: `×`, `÷`, `−`  
✅ **คำนวณซับซ้อน**: วงเล็บ, ลำดับการคำนวณ  
✅ **ฟังก์ชันคณิตศาสตร์**: sqrt, sin, cos, log  
✅ **ป้องกันข้อผิดพลาด**: ตัวเลขใหญ่เกินไป, สมการผิด  

### เคล็ดลับ:

- ใช้ `×` และ `÷` สำหรับการคูณหารที่ชัดเจน
- ใช้วงเล็บ `()` เพื่อควบคุมลำดับการคำนวณ
- ฟังก์ชันต้องมีวงเล็บ เช่น `sqrt(16)` ไม่ใช่ `sqrt 16`
- Ghost message จะหายไปเมื่อเลื่อนหน้าจอหรือคลิกที่อื่น
