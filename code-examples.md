# Code Auto-Complete Examples

## การใช้งาน Code Mode ในแอป Notes

### 🚀 **ฟีเจอร์หลัก:**

#### 💻 **Multi-Language Support**
- **JavaScript/TypeScript** - เว็บแอปพลิเคชัน
- **Python** - AI, Data Science, Backend
- **Java** - Enterprise applications
- **C/C++** - System programming
- **HTML/CSS** - เว็บไซต์
- **JSON/XML** - ข้อมูลและการกำหนดค่า
- **SQL** - ฐานข้อมูล
- **PHP, Go, Rust, Kotl<PERSON>, Swift** - และอื่นๆ

#### ✨ **IDE Features**
- **Syntax Highlighting** - ไฮไลต์โค้ดตามภาษา
- **Auto-Completion** - เติมโค้ดอัตโนมัติ
- **Error Detection** - ตรวจจับข้อผิดพลาด
- **Code Formatting** - จัดรูปแบบโค้ด
- **Minimap** - แผนที่โค้ด
- **Line Numbers** - เลขบรรทัด

### 🎮 **วิธีการใช้งาน:**

#### **1. เปิด Code Mode:**
- คลิกปุ่ม **"Code Mode"** ในแถบเครื่องมือ
- หรือใช้ shortcut (ถ้ามี)

#### **2. เลือกภาษา:**
- เลือกภาษาจาก dropdown menu
- ระบบจะเปลี่ยน syntax highlighting อัตโนมัติ

#### **3. เขียนโค้ด:**
- พิมพ์โค้ดใน Monaco Editor
- ใช้ auto-completion (Ctrl+Space)
- ดู error highlights แบบ real-time

#### **4. รันโค้ด (JavaScript):**
- กด **Ctrl+Enter** เพื่อรันโค้ด JavaScript
- ดูผลลัพธ์ใน Result Panel

#### **5. กลับสู่ Text Mode:**
- คลิกปุ่ม **"Text Mode"** เพื่อกลับ
- โค้ดจะถูกย้ายไปยัง text editor

### 📝 **ตัวอย่างโค้ด:**

#### **JavaScript:**
```javascript
// Auto-completion สำหรับ JavaScript
function calculateSum(numbers) {
    return numbers.reduce((sum, num) => sum + num, 0);
}

const result = calculateSum([1, 2, 3, 4, 5]);
console.log(`Sum: ${result}`);
```

#### **Python:**
```python
# Auto-completion สำหรับ Python
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

for i in range(10):
    print(f"F({i}) = {fibonacci(i)}")
```

#### **HTML:**
```html
<!-- Auto-completion สำหรับ HTML -->
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Website</title>
</head>
<body>
    <h1>สวัสดีชาวโลก!</h1>
    <p>นี่คือเว็บไซต์แรกของฉัน</p>
</body>
</html>
```

#### **CSS:**
```css
/* Auto-completion สำหรับ CSS */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 24px;
    transition: transform 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
}
```

### 🔧 **Code Snippets ที่รองรับ:**

#### **JavaScript:**
- `console.log` → `console.log(message);`
- `function` → `function name(params) { // code }`
- `if` → `if (condition) { // code }`
- `for` → `for (let i = 0; i < length; i++) { // code }`

#### **Python:**
- `print` → `print(message)`
- `def` → `def function_name(params): pass`
- `if` → `if condition: pass`
- `for` → `for item in iterable: pass`

### 🎯 **คุณสมบัติพิเศษ:**

#### **✅ JavaScript Execution:**
- รันโค้ด JavaScript ได้จริงในเบราว์เซอร์
- แสดงผลลัพธ์ใน Result Panel
- จับ console.log output
- แสดง error messages

#### **✅ Smart Auto-Completion:**
- เติมโค้ดตามบริบท
- แสดง parameter hints
- รองรับ IntelliSense

#### **✅ Error Detection:**
- ตรวจจับ syntax errors
- แสดง error markers
- hover เพื่อดู error details

#### **✅ Code Formatting:**
- จัดรูปแบบโค้ดอัตโนมัติ
- รองรับ format on type
- format on paste

### 🚀 **การใช้งานขั้นสูง:**

#### **Keyboard Shortcuts:**
- `Ctrl+Space` - เปิด auto-completion
- `Ctrl+Enter` - รันโค้ด (JavaScript)
- `Ctrl+/` - comment/uncomment
- `Alt+Shift+F` - format code
- `F12` - go to definition

#### **Multi-Cursor:**
- `Alt+Click` - เพิ่ม cursor
- `Ctrl+Alt+Down` - เพิ่ม cursor ด้านล่าง
- `Ctrl+D` - select next occurrence

### 💡 **เคล็ดลับ:**

1. **ใช้ auto-completion** - กด Ctrl+Space เพื่อดู suggestions
2. **ดู error hints** - hover เมาส์เหนือ error markers
3. **ใช้ minimap** - เพื่อนำทางในไฟล์ใหญ่
4. **รันโค้ด JavaScript** - ทดสอบโค้ดได้ทันที
5. **เปลี่ยนภาษา** - เพื่อใช้ syntax highlighting ที่เหมาะสม

### 🔄 **การซิงค์ข้อมูล:**
- เมื่อเปลี่ยนจาก Code Mode เป็น Text Mode โค้ดจะถูกย้ายอัตโนมัติ
- เมื่อเปลี่ยนจาก Text Mode เป็น Code Mode ข้อความจะถูกโหลดใน editor
- การบันทึกไฟล์จะรวมเนื้อหาจากทั้งสองโหมด
