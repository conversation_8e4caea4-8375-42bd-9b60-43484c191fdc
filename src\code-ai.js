'use strict';

/**
 * Code Auto-Complete System using Monaco Editor
 * 
 * Supports multiple programming languages:
 * - JavaScript/TypeScript
 * - Python
 * - Java
 * - C/C++
 * - HTML/CSS
 * - JSON
 * - And many more...
 * 
 * Features:
 * - Syntax highlighting
 * - Auto-completion
 * - Error detection
 * - Code formatting
 * - Multi-language support
 */

class CodeAutoComplete {
    constructor() {
        this.monacoEditor = null;
        this.currentLanguage = 'javascript';
        this.isCodeMode = false;
        this.originalEditor = null;
        this.codeContainer = null;
        this.languageSelector = null;
        this.init();
    }

    init() {
        this.loadMonacoEditor();
        this.createCodeInterface();
        this.bindEvents();
    }

    loadMonacoEditor() {
        // Load Monaco Editor from CDN
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs/loader.js';
        script.onload = () => {
            require.config({ paths: { vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs' } });
            require(['vs/editor/editor.main'], () => {
                console.log('Monaco Editor loaded successfully');
                this.setupMonacoEditor();
            });
        };
        document.head.appendChild(script);
    }

    createCodeInterface() {
        // Create code mode toggle button
        const editorActions = document.querySelector('.editor-actions');
        if (editorActions) {
            const codeToggleBtn = document.createElement('button');
            codeToggleBtn.className = 'btn';
            codeToggleBtn.id = 'codeToggleBtn';
            codeToggleBtn.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" height="20px" viewBox="0 -960 960 960" width="20px" fill="currentColor">
                    <path d="M320-240 80-480l240-240 57 57-184 184 183 183-56 56Zm320 0-57-57 184-184-183-183 56-56 240 240-240 240Z"/>
                </svg>
                Code Mode
            `;
            editorActions.appendChild(codeToggleBtn);
        }

        // Create language selector
        const languageSelector = document.createElement('select');
        languageSelector.id = 'languageSelector';
        languageSelector.className = 'language-selector';
        languageSelector.style.display = 'none';
        languageSelector.innerHTML = `
            <option value="javascript">JavaScript</option>
            <option value="typescript">TypeScript</option>
            <option value="python">Python</option>
            <option value="java">Java</option>
            <option value="cpp">C++</option>
            <option value="c">C</option>
            <option value="html">HTML</option>
            <option value="css">CSS</option>
            <option value="json">JSON</option>
            <option value="xml">XML</option>
            <option value="sql">SQL</option>
            <option value="php">PHP</option>
            <option value="go">Go</option>
            <option value="rust">Rust</option>
            <option value="kotlin">Kotlin</option>
            <option value="swift">Swift</option>
        `;
        
        if (editorActions) {
            editorActions.appendChild(languageSelector);
        }
        this.languageSelector = languageSelector;

        // Create Monaco Editor container
        const editorContainer = document.querySelector('.editor-container');
        if (editorContainer) {
            const codeContainer = document.createElement('div');
            codeContainer.id = 'monacoEditorContainer';
            codeContainer.style.cssText = `
                width: 100%;
                height: 100%;
                display: none;
                border: 1px solid var(--na-border);
                border-radius: var(--na-radius-md);
                overflow: hidden;
            `;
            editorContainer.appendChild(codeContainer);
            this.codeContainer = codeContainer;
        }
    }

    setupMonacoEditor() {
        if (!this.codeContainer) return;

        // Monaco Editor configuration
        this.monacoEditor = monaco.editor.create(this.codeContainer, {
            value: this.getDefaultCode('javascript'),
            language: 'javascript',
            theme: 'vs-dark',
            automaticLayout: true,
            fontSize: 14,
            lineNumbers: 'on',
            roundedSelection: false,
            scrollBeyondLastLine: false,
            readOnly: false,
            minimap: { enabled: true },
            suggestOnTriggerCharacters: true,
            quickSuggestions: true,
            wordBasedSuggestions: true,
            parameterHints: { enabled: true },
            autoClosingBrackets: 'always',
            autoClosingQuotes: 'always',
            formatOnPaste: true,
            formatOnType: true,
        });

        // Add custom completions for common patterns
        this.addCustomCompletions();
    }

    addCustomCompletions() {
        // JavaScript/TypeScript completions
        monaco.languages.registerCompletionItemProvider('javascript', {
            provideCompletionItems: (model, position) => {
                const suggestions = [
                    {
                        label: 'console.log',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: 'console.log(${1:message});',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Log a message to the console'
                    },
                    {
                        label: 'function',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: 'function ${1:name}(${2:params}) {\n\t${3:// code}\n}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Create a function'
                    },
                    {
                        label: 'if',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: 'if (${1:condition}) {\n\t${2:// code}\n}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'If statement'
                    },
                    {
                        label: 'for',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: 'for (let ${1:i} = 0; ${1:i} < ${2:length}; ${1:i}++) {\n\t${3:// code}\n}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'For loop'
                    }
                ];
                return { suggestions };
            }
        });

        // Python completions
        monaco.languages.registerCompletionItemProvider('python', {
            provideCompletionItems: (model, position) => {
                const suggestions = [
                    {
                        label: 'print',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: 'print(${1:message})',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Print a message'
                    },
                    {
                        label: 'def',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: 'def ${1:function_name}(${2:params}):\n\t${3:pass}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Define a function'
                    },
                    {
                        label: 'if',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: 'if ${1:condition}:\n\t${2:pass}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'If statement'
                    },
                    {
                        label: 'for',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: 'for ${1:item} in ${2:iterable}:\n\t${3:pass}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'For loop'
                    }
                ];
                return { suggestions };
            }
        });
    }

    bindEvents() {
        // Code mode toggle
        const codeToggleBtn = document.getElementById('codeToggleBtn');
        if (codeToggleBtn) {
            codeToggleBtn.addEventListener('click', () => {
                this.toggleCodeMode();
            });
        }

        // Language selector
        if (this.languageSelector) {
            this.languageSelector.addEventListener('change', (e) => {
                this.changeLanguage(e.target.value);
            });
        }

        // Sync content between editors
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                if (this.isCodeMode) {
                    this.runCode();
                }
            }
        });
    }

    toggleCodeMode() {
        this.isCodeMode = !this.isCodeMode;
        const originalEditor = document.getElementById('editor');
        const codeToggleBtn = document.getElementById('codeToggleBtn');

        if (this.isCodeMode) {
            // Switch to code mode
            originalEditor.style.display = 'none';
            this.codeContainer.style.display = 'block';
            this.languageSelector.style.display = 'inline-block';
            codeToggleBtn.textContent = 'Text Mode';
            
            // Transfer content from text editor to Monaco
            if (this.monacoEditor && originalEditor.value) {
                this.monacoEditor.setValue(originalEditor.value);
            }
        } else {
            // Switch to text mode
            originalEditor.style.display = 'block';
            this.codeContainer.style.display = 'none';
            this.languageSelector.style.display = 'none';
            codeToggleBtn.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" height="20px" viewBox="0 -960 960 960" width="20px" fill="currentColor">
                    <path d="M320-240 80-480l240-240 57 57-184 184 183 183-56 56Zm320 0-57-57 184-184-183-183 56-56 240 240-240 240Z"/>
                </svg>
                Code Mode
            `;
            
            // Transfer content from Monaco to text editor
            if (this.monacoEditor) {
                originalEditor.value = this.monacoEditor.getValue();
                originalEditor.dispatchEvent(new Event('input', { bubbles: true }));
            }
        }
    }

    changeLanguage(language) {
        this.currentLanguage = language;
        if (this.monacoEditor) {
            const currentValue = this.monacoEditor.getValue();
            const isEmpty = !currentValue.trim();
            
            monaco.editor.setModelLanguage(this.monacoEditor.getModel(), language);
            
            // Set default code for new language if editor is empty
            if (isEmpty) {
                this.monacoEditor.setValue(this.getDefaultCode(language));
            }
        }
    }

    getDefaultCode(language) {
        const templates = {
            javascript: `// JavaScript Example
function greet(name) {
    console.log(\`Hello, \${name}!\`);
}

greet('World');`,
            
            python: `# Python Example
def greet(name):
    print(f"Hello, {name}!")

greet("World")`,
            
            java: `// Java Example
public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
    }
}`,
            
            cpp: `// C++ Example
#include <iostream>
using namespace std;

int main() {
    cout << "Hello, World!" << endl;
    return 0;
}`,
            
            html: `<!-- HTML Example -->
<!DOCTYPE html>
<html>
<head>
    <title>Hello World</title>
</head>
<body>
    <h1>Hello, World!</h1>
</body>
</html>`,
            
            css: `/* CSS Example */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f0f0f0;
}

h1 {
    color: #333;
    text-align: center;
}`,
            
            json: `{
  "name": "example",
  "version": "1.0.0",
  "description": "JSON Example",
  "main": "index.js",
  "scripts": {
    "start": "node index.js"
  }
}`
        };
        
        return templates[language] || `// ${language.toUpperCase()} code here...`;
    }

    runCode() {
        if (!this.monacoEditor) return;

        const code = this.monacoEditor.getValue();
        const language = this.currentLanguage;

        // Try to execute JavaScript code in a safe way
        if (language === 'javascript') {
            try {
                // Create a safe execution environment
                const result = this.executeJavaScript(code);
                this.showCodeResult(result, 'success');
            } catch (error) {
                this.showCodeResult(error.message, 'error');
            }
        } else {
            // For other languages, show simulation message
            this.showNotification(`Code execution simulated for ${language}`);
            console.log(`${language} code:`, code);
        }
    }

    executeJavaScript(code) {
        // Create a safe sandbox for JavaScript execution
        const originalConsoleLog = console.log;
        const logs = [];

        // Override console.log to capture output
        console.log = (...args) => {
            logs.push(args.map(arg =>
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' '));
        };

        try {
            // Execute the code
            const result = Function('"use strict"; ' + code)();

            // Restore console.log
            console.log = originalConsoleLog;

            // Return captured logs and result
            const output = logs.length > 0 ? logs.join('\n') : '';
            const finalResult = result !== undefined ? String(result) : '';

            return output + (output && finalResult ? '\n' : '') + finalResult;
        } catch (error) {
            // Restore console.log
            console.log = originalConsoleLog;
            throw error;
        }
    }

    showCodeResult(result, type = 'success') {
        // Create or update result panel
        let resultPanel = document.getElementById('codeResultPanel');

        if (!resultPanel) {
            resultPanel = document.createElement('div');
            resultPanel.id = 'codeResultPanel';
            resultPanel.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                max-width: 400px;
                max-height: 300px;
                background: var(--na-surface);
                border: 1px solid var(--na-border);
                border-radius: var(--na-radius-md);
                padding: 16px;
                z-index: 10000;
                overflow: auto;
                font-family: var(--na-font-mono);
                font-size: var(--na-text-sm);
                box-shadow: var(--na-shadow-lg);
                animation: slideIn 0.3s ease;
            `;

            const closeBtn = document.createElement('button');
            closeBtn.innerHTML = '×';
            closeBtn.style.cssText = `
                position: absolute;
                top: 8px;
                right: 8px;
                background: none;
                border: none;
                color: var(--na-text-primary);
                font-size: 18px;
                cursor: pointer;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
            `;
            closeBtn.addEventListener('click', () => resultPanel.remove());

            resultPanel.appendChild(closeBtn);
            document.body.appendChild(resultPanel);
        }

        const resultContent = document.createElement('div');
        resultContent.style.cssText = `
            color: ${type === 'error' ? '#ff6b6b' : '#4ecdc4'};
            white-space: pre-wrap;
            margin-top: 20px;
        `;
        resultContent.textContent = result || 'No output';

        // Clear previous results and add new one
        const existingContent = resultPanel.querySelector('div:not(:first-child)');
        if (existingContent) {
            existingContent.remove();
        }

        resultPanel.appendChild(resultContent);
    }

    showNotification(message) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--na-primary);
            color: white;
            padding: 12px 20px;
            border-radius: var(--na-radius-md);
            z-index: 10000;
            animation: slideIn 0.3s ease;
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        window.codeAutoComplete = new CodeAutoComplete();
        console.log('Code Auto-Complete initialized');
    }, 200);
});

// Export for potential external use
window.CodeAutoComplete = CodeAutoComplete;
