'use strict';

class MathAutoComplete {
    constructor() {
        this.ghostElement = null;
        this.currentSuggestion = null;
        this.isActive = false;
        this.init();
    }

    init() {
        this.createGhostElement();
        this.bindEvents();
    }

    createGhostElement() {
        this.ghostElement = document.createElement('div');
        this.ghostElement.className = 'math-ghost-suggestion';
        this.ghostElement.style.cssText = `
            position: absolute;
            color: rgba(255, 255, 255, 0.4);
            pointer-events: auto;
            cursor: pointer;
            font-family: inherit;
            font-size: inherit;
            line-height: inherit;
            white-space: pre;
            z-index: 1000;
            display: none;
            user-select: none;
            background: rgba(0, 180, 216, 0.1);
            border-radius: 3px;
            padding: 0 2px;
            transition: all 0.2s ease;
        `;
        
        this.ghostElement.addEventListener('click', () => {
            this.acceptSuggestion();
        });

        this.ghostElement.addEventListener('mouseenter', () => {
            this.ghostElement.style.background = 'rgba(0, 180, 216, 0.2)';
        });

        this.ghostElement.addEventListener('mouseleave', () => {
            this.ghostElement.style.background = 'rgba(0, 180, 216, 0.1)';
        });

        document.body.appendChild(this.ghostElement);
    }

    bindEvents() {
        const editor = document.getElementById('editor');
        if (!editor) return;

        editor.addEventListener('input', (e) => {
            this.handleInput(e);
        });

        editor.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        });

        editor.addEventListener('scroll', () => {
            this.hideGhost();
        });

        // Hide ghost when clicking elsewhere
        document.addEventListener('click', (e) => {
            if (e.target !== this.ghostElement && e.target !== editor) {
                this.hideGhost();
            }
        });
    }

    handleInput(event) {
        const editor = event.target;
        const text = editor.value;
        const cursorPos = editor.selectionStart;
        
        // Get current line
        const lines = text.substring(0, cursorPos).split('\n');
        const currentLine = lines[lines.length - 1];
        
        this.checkForMathExpression(currentLine, editor, cursorPos);
    }

    handleKeydown(event) {
        if (this.isActive) {
            if (event.key === 'Tab' || event.key === 'ArrowRight') {
                event.preventDefault();
                this.acceptSuggestion();
            } else if (event.key === 'Escape') {
                this.hideGhost();
            }
        }
    }

    checkForMathExpression(line, editor, cursorPos) {
        // Math expression patterns - more flexible
        const patterns = [
            // Basic arithmetic with equals: 1+1=, 5-3=, 2*4=, 8/2=
            /^(.+?)\s*=\s*$/,
            // Just numbers and operators without equals (for preview)
            /^(\d+(?:\.\d+)?)\s*([+\-*/])\s*(\d+(?:\.\d+)?)$/
        ];

        for (const pattern of patterns) {
            const match = line.match(pattern);
            if (match) {
                let expression;
                if (pattern.source.includes('=')) {
                    // Has equals sign
                    expression = match[1].trim();
                } else {
                    // No equals sign, use full match
                    expression = match[0].trim();
                }

                const result = this.evaluateExpression(expression);

                if (result !== null) {
                    // Check if line ends with = to determine suggestion format
                    const suggestion = line.includes('=') ? result : ` = ${result}`;
                    this.showGhost(suggestion, editor, cursorPos);
                    return;
                }
            }
        }

        this.hideGhost();
    }

    evaluateExpression(expr) {
        try {
            // Clean the expression - allow more mathematical characters
            let cleanExpr = expr.replace(/[^0-9+\-*/().\s^%]/g, '');

            // Replace ^ with ** for JavaScript power operator
            cleanExpr = cleanExpr.replace(/\^/g, '**');

            // Basic validation
            if (!cleanExpr || cleanExpr.trim() === '') return null;

            // Additional safety checks
            if (cleanExpr.includes('**') && cleanExpr.split('**').length > 3) {
                // Prevent excessive power operations
                return null;
            }

            // Evaluate safely with timeout protection
            const result = Function('"use strict"; return (' + cleanExpr + ')')();

            // Check if result is a valid number
            if (typeof result === 'number' && !isNaN(result) && isFinite(result)) {
                // Prevent extremely large numbers
                if (Math.abs(result) > 1e15) {
                    return 'Too large';
                }
                // Format the result nicely
                return this.formatResult(result);
            }

            return null;
        } catch (error) {
            return null;
        }
    }

    formatResult(result) {
        // Round to reasonable decimal places
        if (result % 1 === 0) {
            return result.toString();
        } else {
            return parseFloat(result.toFixed(6)).toString();
        }
    }

    showGhost(suggestion, editor, cursorPos) {
        this.currentSuggestion = suggestion;
        this.isActive = true;

        // Calculate position more accurately
        const rect = editor.getBoundingClientRect();
        const textBeforeCursor = editor.value.substring(0, cursorPos);
        const lines = textBeforeCursor.split('\n');
        const currentLineIndex = lines.length - 1;
        const currentLineText = lines[currentLineIndex];

        // Create a temporary element to measure text with exact same styling
        const measurer = document.createElement('div');
        const editorStyles = getComputedStyle(editor);
        measurer.style.cssText = `
            position: absolute;
            visibility: hidden;
            white-space: pre;
            font-family: ${editorStyles.fontFamily};
            font-size: ${editorStyles.fontSize};
            line-height: ${editorStyles.lineHeight};
            font-weight: ${editorStyles.fontWeight};
            letter-spacing: ${editorStyles.letterSpacing};
            padding: 0;
            margin: 0;
            border: 0;
        `;
        measurer.textContent = currentLineText;
        document.body.appendChild(measurer);

        const textWidth = measurer.offsetWidth;
        const lineHeight = parseFloat(editorStyles.lineHeight) || 24;

        document.body.removeChild(measurer);

        // Calculate scroll offset
        const scrollTop = editor.scrollTop;
        const scrollLeft = editor.scrollLeft;

        // Position the ghost with proper spacing
        const paddingLeft = parseInt(editorStyles.paddingLeft) || 0;
        const paddingTop = parseInt(editorStyles.paddingTop) || 0;

        const left = rect.left + textWidth + paddingLeft - scrollLeft;
        const top = rect.top + (currentLineIndex * lineHeight) + paddingTop - scrollTop;

        // Format suggestion with proper spacing
        const formattedSuggestion = suggestion.startsWith(' ') ? suggestion : ` ${suggestion}`;

        this.ghostElement.textContent = formattedSuggestion;
        this.ghostElement.style.left = left + 'px';
        this.ghostElement.style.top = top + 'px';
        this.ghostElement.style.display = 'block';

        // Add a subtle animation
        this.ghostElement.style.opacity = '0';
        this.ghostElement.style.transform = 'translateX(-5px)';

        requestAnimationFrame(() => {
            this.ghostElement.style.opacity = '1';
            this.ghostElement.style.transform = 'translateX(0)';
        });
    }

    hideGhost() {
        this.ghostElement.style.display = 'none';
        this.isActive = false;
        this.currentSuggestion = null;
    }

    acceptSuggestion() {
        if (!this.isActive || !this.currentSuggestion) return;

        const editor = document.getElementById('editor');
        if (!editor) return;

        const cursorPos = editor.selectionStart;
        const text = editor.value;

        // Clean the suggestion (remove leading space if it exists)
        const cleanSuggestion = this.currentSuggestion.replace(/^\s+/, '');

        // Insert the suggestion - it already includes proper spacing
        const suggestionToInsert = this.currentSuggestion.startsWith(' ') ?
            this.currentSuggestion : ` ${cleanSuggestion}`;

        const newText = text.substring(0, cursorPos) + suggestionToInsert + text.substring(cursorPos);
        editor.value = newText;

        // Move cursor to end of inserted text
        const newCursorPos = cursorPos + suggestionToInsert.length;
        editor.setSelectionRange(newCursorPos, newCursorPos);

        // Trigger input event to update word count, etc.
        editor.dispatchEvent(new Event('input', { bubbles: true }));

        this.hideGhost();
        editor.focus();
    }
}

// Advanced math functions
class AdvancedMathAutoComplete extends MathAutoComplete {
    constructor() {
        super();
        this.mathFunctions = {
            'sqrt': Math.sqrt,
            'sin': Math.sin,
            'cos': Math.cos,
            'tan': Math.tan,
            'log': Math.log10,
            'ln': Math.log,
            'abs': Math.abs,
            'round': Math.round,
            'floor': Math.floor,
            'ceil': Math.ceil,
            'pow': Math.pow
        };
    }

    checkForMathExpression(line, editor, cursorPos) {
        // Check for function calls like sqrt(16)=, sin(30)=
        const functionPattern = /(\w+)\s*\(\s*([^)]+)\s*\)\s*=\s*$/;
        const functionMatch = line.match(functionPattern);
        
        if (functionMatch) {
            const funcName = functionMatch[1].toLowerCase();
            const argument = functionMatch[2];
            
            if (this.mathFunctions[funcName]) {
                try {
                    const argValue = parseFloat(argument);
                    if (!isNaN(argValue)) {
                        let result;
                        if (funcName === 'pow') {
                            // Handle power function specially (needs two arguments)
                            const args = argument.split(',').map(x => parseFloat(x.trim()));
                            if (args.length === 2 && !isNaN(args[0]) && !isNaN(args[1])) {
                                result = Math.pow(args[0], args[1]);
                            }
                        } else {
                            result = this.mathFunctions[funcName](argValue);
                        }
                        
                        if (result !== undefined && !isNaN(result) && isFinite(result)) {
                            this.showGhost(this.formatResult(result), editor, cursorPos);
                            return;
                        }
                    }
                } catch (error) {
                    // Ignore errors
                }
            }
        }
        
        // Fall back to basic math
        super.checkForMathExpression(line, editor, cursorPos);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Wait a bit for other scripts to initialize
    setTimeout(() => {
        window.mathAutoComplete = new AdvancedMathAutoComplete();
        console.log('Math Auto-Complete initialized');
    }, 100);
});

// Export for potential external use
window.MathAutoComplete = MathAutoComplete;
window.AdvancedMathAutoComplete = AdvancedMathAutoComplete;
